package com.ilkeradanur.personalsite.service;

import com.ilkeradanur.personalsite.entity.Skill;
import com.ilkeradanur.personalsite.repository.SkillRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class SkillService {

    private final SkillRepository skillRepository;

    @Autowired
    public SkillService(SkillRepository skillRepository) {
        this.skillRepository = skillRepository;
    }

    public List<Skill> getAllSkills() {
        return skillRepository.findAll();
    }

    public List<Skill> getActiveSkills() {
        return skillRepository.findByIsActiveOrderByDisplayOrderAsc(true);
    }

    public List<Skill> getActiveSkillsByCategory(String category) {
        return skillRepository.findByCategoryAndIsActiveOrderByDisplayOrderAsc(category, true);
    }

    public Optional<Skill> getSkillById(Long id) {
        return skillRepository.findById(id);
    }

    public Skill saveSkill(Skill skill) {
        if (skill.getDisplayOrder() == null) {
            List<Skill> skills = skillRepository.findAll();
            skill.setDisplayOrder(skills.size() + 1);
        }
        return skillRepository.save(skill);
    }

    public void deleteSkill(Long id) {
        skillRepository.deleteById(id);
    }

    @Transactional
    public void updateSkillOrder(List<Long> skillIds) {
        for (int i = 0; i < skillIds.size(); i++) {
            Skill skill = skillRepository.findById(skillIds.get(i)).orElse(null);
            if (skill != null) {
                skill.setDisplayOrder(i + 1);
                skillRepository.save(skill);
            }
        }
    }
} 