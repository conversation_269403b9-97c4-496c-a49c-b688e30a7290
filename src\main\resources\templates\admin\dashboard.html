<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}">Admin Paneli</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Admin Paneli</h1>
                    <form th:action="@{/logout}" method="post" style="display: inline;">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-sign-out-alt"></i> Çıkış Yap
                        </button>
                    </form>
                </div>

                <div class="row">
                    <!-- Yetenekler Kartı -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-code text-primary"></i>
                                    Yetenekler
                                </h5>
                                <p class="card-text">
                                    Programlama dilleri, framework'ler ve diğer yeteneklerinizi yönetin.
                                </p>
                                <a href="/admin/skills" class="btn btn-primary">
                                    <i class="fas fa-cog"></i> Yetenekleri Düzenle
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Projeler Kartı -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-project-diagram text-success"></i>
                                    Projeler
                                </h5>
                                <p class="card-text">
                                    Kişisel projelerinizi ekleyin, düzenleyin ve yönetin.
                                </p>
                                <a href="/admin/projects" class="btn btn-success">
                                    <i class="fas fa-cog"></i> Projeleri Düzenle
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Hakkımda Kartı -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-user text-info"></i>
                                    Hakkımda
                                </h5>
                                <p class="card-text">
                                    Kişisel bilgilerinizi ve özgeçmişinizi güncelleyin.
                                </p>
                                <a href="/admin/about" class="btn btn-info text-white">
                                    <i class="fas fa-cog"></i> Hakkımda'yı Düzenle
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Zaman Çizelgesi Kartı -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-history text-warning"></i>
                                    Zaman Çizelgesi
                                </h5>
                                <p class="card-text">
                                    Eğitim ve iş deneyimlerinizi kronolojik olarak düzenleyin.
                                </p>
                                <a href="/admin/timeline" class="btn btn-warning text-white">
                                    <i class="fas fa-cog"></i> Zaman Çizelgesini Düzenle
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Site Önizleme Kartı -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-eye text-secondary"></i>
                                    Site Önizleme
                                </h5>
                                <p class="card-text">
                                    Yaptığınız değişiklikleri canlı olarak görüntüleyin.
                                </p>
                                <a href="/" class="btn btn-secondary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> Siteyi Görüntüle
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 