package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Message;
import com.ilkeradanur.personal_website.repository.MessageRepository;
import org.springframework.stereotype.Service;
import jakarta.persistence.EntityNotFoundException;

import java.util.List;
import com.ilkeradanur.personal_website.dto.ContactFormDTO;
import java.time.LocalDateTime;

@Service
public class MessageServiceImpl implements MessageService {

    private final MessageRepository messageRepository;

    public MessageServiceImpl(MessageRepository messageRepository) {
        this.messageRepository = messageRepository;
    }

    @Override
    public Message saveMessage(Message message) {
        return messageRepository.save(message);
    }

    @Override
    public List<Message> getAllMessages() {
        return messageRepository.findAll();
    }

    @Override
    public void deleteMessage(Long id) {
        if (!messageRepository.existsById(id)) {
            throw new EntityNotFoundException("Mesaj bulunamadı: " + id);
        }
        messageRepository.deleteById(id);
    }

    @Override
    public List<Message> getUnreadMessages() {
        return messageRepository.findByReadFalse();
    }

    @Override
    public Message markAsRead(Long id) {
        Message message = messageRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Mesaj bulunamadı: " + id));
        message.setRead(true);
        return messageRepository.save(message);
    }

    @Override
    public Message saveMessageFromDTO(ContactFormDTO contactFormDTO) {
        Message message = new Message();
        message.setName(contactFormDTO.getName());
        message.setEmail(contactFormDTO.getEmail());
        message.setSubject(contactFormDTO.getSubject());
        message.setMessage(contactFormDTO.getMessage());
        // timestamp is set automatically by @PrePersist in Message entity
        // read status defaults to false in Message entity
        return messageRepository.save(message);
    }
} 