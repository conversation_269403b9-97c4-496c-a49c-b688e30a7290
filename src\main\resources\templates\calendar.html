<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON><PERSON> - <PERSON>">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>m Projeleri">
    <meta name="author" content="<PERSON>lk<PERSON>nu<PERSON>">
    
    <title><PERSON><PERSON><PERSON>izelge<PERSON></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">
    
    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Navigasyon -->
    <nav class="nav">
        <div class="nav__container container">
            <a href="/" class="nav__logo">İlker Adanur</a>
            
            <button class="nav__theme-toggle" aria-label="Tema değiştir">
                <i class="fas fa-moon"></i>
            </button>
            
            <ul class="nav__menu">
                <li><a href="/#about" class="nav__link">Hakkımda</a></li>
                <li><a href="/#skills" class="nav__link">Yetenekler</a></li>
                <li><a href="/#projects" class="nav__link">Projeler</a></li>
                <li><a href="/#contact" class="nav__link">İletişim</a></li>
                <li><a href="/timeline" class="nav__link nav__link--active">Zaman Çizelgesi</a></li>
            </ul>
        </div>
    </nav>

    <!-- Zaman Çizelgesi Bölümü -->
    <section class="timeline-section" id="timeline">
        <div class="container">
            <div class="timeline-section__header text-center">
                <h1>Zaman Çizelgesi</h1>
                <p class="text-secondary">Hayatımın önemli dönemleri ve projelerim</p>
            </div>
            
            <div class="timeline">
                <!-- 2022-2026 Samsun Üniversitesi -->
                <div class="timeline__period">
                    <div class="timeline__period-header">
                        <h2>2022 - 2026 Samsun Üniversitesi</h2>
                        <p class="timeline__period-subtitle">Bilgisayar Mühendisliği</p>
                    </div>
                    
                    <div class="timeline__projects">
                        <div class="project-card" data-category="ai">
                            <div class="project-card__header">
                                <i class="fas fa-robot project-card__icon"></i>
                                <h3>AI Chat Uygulaması</h3>
                            </div>
                            <p class="project-card__description">
                                Google Colab ve Deepseek API kullanılarak geliştirilen yapay zeka sohbet uygulaması.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">AI</span>
                                <span class="tag">Python</span>
                                <span class="tag">Google Colab</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="desktop">
                            <div class="project-card__header">
                                <i class="fas fa-desktop project-card__icon"></i>
                                <h3>AI Desktop Assistant</h3>
                            </div>
                            <p class="project-card__description">
                                Windows 11 için geliştirilen yapay zeka destekli masaüstü asistanı.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Windows</span>
                                <span class="tag">Python</span>
                                <span class="tag">AI</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="mobile">
                            <div class="project-card__header">
                                <i class="fas fa-mobile-alt project-card__icon"></i>
                                <h3>Fractional Stock Transfer Mobil Uygulaması</h3>
                            </div>
                            <p class="project-card__description">
                                Rork.app platformu için geliştirilen kesirli hisse transfer uygulaması.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Mobile</span>
                                <span class="tag">React Native</span>
                                <span class="tag">Fintech</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="web">
                            <div class="project-card__header">
                                <i class="fas fa-globe project-card__icon"></i>
                                <h3>Kişisel Web Sitesi</h3>
                            </div>
                            <p class="project-card__description">
                                Spring Boot ve modern web teknolojileri ile geliştirilen kişisel web sitesi.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Web</span>
                                <span class="tag">Spring Boot</span>
                                <span class="tag">SQLite</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="mobile">
                            <div class="project-card__header">
                                <i class="fas fa-graduation-cap project-card__icon"></i>
                                <h3>React Native Eğitim Uygulaması</h3>
                            </div>
                            <p class="project-card__description">
                                Mobil platformlar için geliştirilen eğitim ve öğrenme uygulaması.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Mobile</span>
                                <span class="tag">React Native</span>
                                <span class="tag">Education</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="mobile">
                            <div class="project-card__header">
                                <i class="fas fa-language project-card__icon"></i>
                                <h3>Dil Öğrenme Uygulaması</h3>
                            </div>
                            <p class="project-card__description">
                                GitHub üzerinde açık kaynak olarak geliştirilen dil öğrenme uygulaması.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Mobile</span>
                                <span class="tag">Open Source</span>
                                <span class="tag">Education</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="backend">
                            <div class="project-card__header">
                                <i class="fas fa-boxes project-card__icon"></i>
                                <h3>Stok Takip Sistemi</h3>
                            </div>
                            <p class="project-card__description">
                                Spring Boot ve SQLite ile geliştirilen kurumsal stok takip sistemi.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Backend</span>
                                <span class="tag">Spring Boot</span>
                                <span class="tag">SQLite</span>
                            </div>
                        </div>

                        <div class="project-card" data-category="desktop">
                            <div class="project-card__header">
                                <i class="fas fa-window-maximize project-card__icon"></i>
                                <h3>Java Swing Görsel Programlama Projeleri</h3>
                            </div>
                            <p class="project-card__description">
                                Java Swing kullanılarak geliştirilen masaüstü uygulamaları.
                            </p>
                            <div class="project-card__tags">
                                <span class="tag">Desktop</span>
                                <span class="tag">Java</span>
                                <span class="tag">Swing</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Diğer dönemler buraya eklenecek -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__social">
                    <a href="https://github.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/in/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://twitter.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p>&copy; 2024 İlker Adanur. Tüm hakları saklıdır.</p>
            </div>
        </div>
        
        <button class="footer__back-to-top" aria-label="Yukarı çık">
            <i class="fas fa-arrow-up"></i>
        </button>
    </footer>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>
</body>
</html> 