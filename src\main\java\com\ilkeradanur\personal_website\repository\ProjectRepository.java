package com.ilkeradanur.personal_website.repository;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.ProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    // Proje durumuna göre projeleri getir
    List<Project> findByStatus(ProjectStatus status);
    
    // Kategoriye göre projeleri getir
    List<Project> findByCategory(String category);
    
    // Başlangıç tarihine göre projeleri getir
    List<Project> findByStartDateAfter(LocalDate date);
    
    // Teknolojiye göre projeleri getir (technologies alanında arama yapar)
    @Query("SELECT p FROM Project p WHERE LOWER(p.technologies) LIKE LOWER(CONCAT('%', :technology, '%'))")
    List<Project> findByTechnology(@Param("technology") String technology);
    
    // Teknoloji ve kategoriye göre projeleri getir
    @Query("SELECT p FROM Project p WHERE " +
           "LOWER(p.category) = LOWER(:category) AND " +
           "LOWER(p.technologies) LIKE LOWER(CONCAT('%', :technology, '%'))")
    List<Project> findByCategoryAndTechnology(@Param("category") String category, 
                                            @Param("technology") String technology);
    
    // Aktif projeleri getir (tamamlanmamış projeler)
    @Query("SELECT p FROM Project p WHERE p.status != 'COMPLETED' ORDER BY p.startDate DESC")
    List<Project> findActiveProjects();
    
    // Son eklenen projeleri getir
    List<Project> findTop5ByOrderByIdDesc();
    
    // Belirli bir tarih aralığındaki projeleri getir
    @Query("SELECT p FROM Project p WHERE p.startDate BETWEEN :startDate AND :endDate")
    List<Project> findProjectsBetweenDates(@Param("startDate") LocalDate startDate, 
                                         @Param("endDate") LocalDate endDate);
    
    // Kategoriye göre projeleri say
    @Query("SELECT p.category, COUNT(p) FROM Project p GROUP BY p.category")
    List<Object[]> countProjectsByCategory();

    // Son eklenen projeleri getir (Native SQL ile SQLite uyumlu)
    @Query(value = "SELECT * FROM projects ORDER BY id DESC LIMIT 5", nativeQuery = true)
    List<Project> findLatest5Projects();
} 