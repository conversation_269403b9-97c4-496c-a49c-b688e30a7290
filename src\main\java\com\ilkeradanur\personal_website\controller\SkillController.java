package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Skill;
import com.ilkeradanur.personal_website.service.SkillService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/skills")
public class SkillController {

    private final SkillService skillService;

    public SkillController(SkillService skillService) {
        this.skillService = skillService;
    }

    // View endpoints
    @GetMapping
    public String skills(Model model) {
        model.addAttribute("skills", skillService.getActiveSkills());
        model.addAttribute("title", "Yeteneklerim");
        model.addAttribute("categoryStats", skillService.getSkillCountByCategory());
        return "skills";
    }

    @GetMapping("/category/{category}")
    public String skillsByCategory(@PathVariable String category, Model model) {
        model.addAttribute("skills", skillService.getActiveSkillsByCategory(category));
        model.addAttribute("title", category + " Yetenekleri");
        model.addAttribute("category", category);
        return "skills";
    }

    @GetMapping("/certified")
    public String certifiedSkills(Model model) {
        model.addAttribute("skills", skillService.getCertifiedSkills());
        model.addAttribute("title", "Sertifikalarım");
        return "skills";
    }

    // REST API endpoints
    @GetMapping("/api")
    @ResponseBody
    public ResponseEntity<List<Skill>> getAllSkills() {
        return ResponseEntity.ok(skillService.getAllSkills());
    }

    @GetMapping("/api/active")
    @ResponseBody
    public ResponseEntity<List<Skill>> getActiveSkills() {
        return ResponseEntity.ok(skillService.getActiveSkills());
    }

    @GetMapping("/api/category/{category}")
    @ResponseBody
    public ResponseEntity<List<Skill>> getSkillsByCategory(@PathVariable String category) {
        return ResponseEntity.ok(skillService.getSkillsByCategory(category));
    }

    @GetMapping("/api/certified")
    @ResponseBody
    public ResponseEntity<List<Skill>> getCertifiedSkills() {
        return ResponseEntity.ok(skillService.getCertifiedSkills());
    }

    @GetMapping("/api/stats/category")
    @ResponseBody
    public ResponseEntity<Map<String, Long>> getSkillCountByCategory() {
        return ResponseEntity.ok(skillService.getSkillCountByCategory());
    }

    @PostMapping("/api")
    @ResponseBody
    public ResponseEntity<Skill> createSkill(@RequestBody Skill skill) {
        try {
            Skill savedSkill = skillService.saveSkill(skill);
            return ResponseEntity.ok(savedSkill);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/api/{id}/toggle")
    @ResponseBody
    public ResponseEntity<Skill> toggleSkillStatus(@PathVariable Long id) {
        try {
            Skill updatedSkill = skillService.toggleSkillStatus(id);
            return ResponseEntity.ok(updatedSkill);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/api/{id}")
    @ResponseBody
    public ResponseEntity<Void> deleteSkill(@PathVariable Long id) {
        try {
            skillService.deleteSkill(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
} 