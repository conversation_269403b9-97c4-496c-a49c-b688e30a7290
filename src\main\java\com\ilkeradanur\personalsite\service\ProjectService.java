package com.ilkeradanur.personalsite.service;

import com.ilkeradanur.personalsite.entity.Project;
import com.ilkeradanur.personalsite.repository.ProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ProjectService {

    private final ProjectRepository projectRepository;

    @Autowired
    public ProjectService(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    public List<Project> getAllProjects() {
        return projectRepository.findAll();
    }

    public List<Project> getActiveProjects() {
        return projectRepository.findByIsActiveOrderByDisplayOrderAsc(true);
    }

    public List<Project> getActiveProjectsByCategory(String category) {
        return projectRepository.findByCategoryAndIsActiveOrderByDisplayOrderAsc(category, true);
    }

    public List<Project> getActiveProjectsByStatus(String status) {
        return projectRepository.findByStatusAndIsActiveOrderByDisplayOrderAsc(status, true);
    }

    public Optional<Project> getProjectById(Long id) {
        return projectRepository.findById(id);
    }

    public Project saveProject(Project project) {
        if (project.getDisplayOrder() == null) {
            List<Project> projects = projectRepository.findAll();
            project.setDisplayOrder(projects.size() + 1);
        }
        return projectRepository.save(project);
    }

    public void deleteProject(Long id) {
        projectRepository.deleteById(id);
    }

    @Transactional
    public void updateProjectOrder(List<Long> projectIds) {
        for (int i = 0; i < projectIds.size(); i++) {
            Project project = projectRepository.findById(projectIds.get(i)).orElse(null);
            if (project != null) {
                project.setDisplayOrder(i + 1);
                projectRepository.save(project);
            }
        }
    }
} 