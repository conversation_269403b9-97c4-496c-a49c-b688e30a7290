package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Skill;
import java.util.List;
import java.util.Map;

public interface SkillService {
    // Temel CRUD işlemleri
    List<Skill> getAllSkills();
    Skill saveSkill(Skill skill);
    void deleteSkill(Long id);
    
    // Kategori işlemleri
    List<Skill> getSkillsByCategory(String category);
    Map<String, Long> getSkillCountByCategory();
    
    // Aktif yetenekler
    List<Skill> getActiveSkills();
    List<Skill> getActiveSkillsByCategory(String category);
    
    // Sertifika işlemleri
    List<Skill> getCertifiedSkills();
    
    // Yetenek durumu işlemleri
    Skill toggleSkillStatus(Long id);
} 