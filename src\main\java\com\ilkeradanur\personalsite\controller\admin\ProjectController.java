package com.ilkeradanur.personalsite.controller.admin;

import com.ilkeradanur.personalsite.entity.Project;
import com.ilkeradanur.personalsite.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
@RequestMapping("/admin/projects")
public class ProjectController {

    private final ProjectService projectService;

    @Autowired
    public ProjectController(ProjectService projectService) {
        this.projectService = projectService;
    }

    @GetMapping
    public String listProjects(Model model) {
        model.addAttribute("projects", projectService.getAllProjects());
        model.addAttribute("project", new Project());
        model.addAttribute("projectStatuses", Project.ProjectStatus.values());
        return "admin/projects";
    }

    @PostMapping
    public String saveProject(@ModelAttribute Project project, RedirectAttributes redirectAttributes) {
        try {
            projectService.saveProject(project);
            redirectAttributes.addFlashAttribute("success", "Proje başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Proje kaydedilirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/projects";
    }

    @PostMapping("/{id}/delete")
    public String deleteProject(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            projectService.deleteProject(id);
            redirectAttributes.addFlashAttribute("success", "Proje başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Proje silinirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/projects";
    }
} 