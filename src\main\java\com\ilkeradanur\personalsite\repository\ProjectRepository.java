package com.ilkeradanur.personalsite.repository;

import com.ilkeradanur.personalsite.entity.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    List<Project> findByIsActiveOrderByDisplayOrderAsc(Boolean isActive);
    List<Project> findByCategoryAndIsActiveOrderByDisplayOrderAsc(String category, Boolean isActive);
    List<Project> findByStatusAndIsActiveOrderByDisplayOrderAsc(String status, Boolean isActive);
} 