package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.ProjectStatus;
import com.ilkeradanur.personal_website.entity.Skill;
import com.ilkeradanur.personal_website.service.ProjectService;
import com.ilkeradanur.personal_website.service.SkillService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
public class HomeController {

    private final ProjectService projectService;
    private final SkillService skillService;

    public HomeController(ProjectService projectService, SkillService skillService) {
        this.projectService = projectService;
        this.skillService = skillService;
    }

    @GetMapping("/")
    public String index(Model model) {
        // Son 6 projeyi getir
        List<Project> allProjects = projectService.getAllProjects();
        List<Project> nonNullProjects = allProjects.stream().filter(p -> p != null).collect(Collectors.toList());
        model.addAttribute("projects", nonNullProjects);
        
        // Aktif yetenekleri getir
        List<Skill> activeSkills = skillService.getActiveSkills();
        model.addAttribute("skills", activeSkills);
        
        // Kategori ve teknoloji istatistiklerini ekle
        model.addAttribute("categoryStats", projectService.getProjectCountByCategory());
        model.addAttribute("technologyStats", projectService.getProjectCountByTechnology());
        model.addAttribute("skillCategoryStats", skillService.getSkillCountByCategory());
        
        // Kategori ve teknoloji listelerini ekle
        model.addAttribute("categories", Arrays.asList(
            new ProjectCategory("web", "Web Geliştirme"),
            new ProjectCategory("mobile", "Mobil Uygulama"),
            new ProjectCategory("backend", "Backend Geliştirme"),
            new ProjectCategory("desktop", "Masaüstü Uygulama"),
            new ProjectCategory("ai", "Yapay Zeka")
        ));
        
        model.addAttribute("skillCategories", Arrays.asList(
            new SkillCategory("programming", "Programlama"),
            new SkillCategory("framework", "Framework"),
            new SkillCategory("database", "Veritabanı"),
            new SkillCategory("tool", "Araç"),
            new SkillCategory("language", "Dil"),
            new SkillCategory("certification", "Sertifika")
        ));
        
        model.addAttribute("technologies", projectService.getAllTechnologies());
        model.addAttribute("statuses", ProjectStatus.values());
        
        return "index";
    }
    
    // Kategori bilgilerini tutmak için iç sınıflar
    private static class ProjectCategory {
        private final String value;
        private final String displayName;
        
        public ProjectCategory(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    private static class SkillCategory {
        private final String value;
        private final String displayName;
        
        public SkillCategory(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }
        
        public String getValue() {
            return value;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
} 