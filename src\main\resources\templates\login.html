<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>
    <div class="login-container">
        <h2>Yönetici Girişi</h2>
        <div th:if="${param.error}" class="alert alert-error">
            Geçersiz kullanıcı adı veya şifre.
        </div>
        <div th:if="${param.logout}" class="alert alert-success">
            Başarıyla çıkış yaptınız.
        </div>
        <form th:action="@{/login}" method="post">
            <div class="form-group">
                <label for="username">E-posta:</label>
                <input type="email" id="username" name="username" value="<EMAIL>" required autofocus>
            </div>
            <div class="form-group">
                <label for="password">Şifre:</label>
                <input type="password" id="password" name="password" value="admin" required>
            </div>
            <button type="submit" class="btn btn-primary">Giriş Yap</button>
        </form>
        <div class="back-link">
            <a th:href="@{/}">Ana Sayfaya Dön</a>
        </div>
    </div>
</body>
</html>