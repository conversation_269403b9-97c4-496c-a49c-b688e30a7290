package com.ilkeradanur.personalsite.repository;

import com.ilkeradanur.personalsite.entity.Timeline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TimelineRepository extends JpaRepository<Timeline, Long> {
    List<Timeline> findByIsActiveOrderByStartDateDesc(Boolean isActive);
    List<Timeline> findByTypeAndIsActiveOrderByStartDateDesc(Timeline.TimelineType type, Boolean isActive);
} 