package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.service.ProjectService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/timeline")
public class TimelineController {
    
    private final ProjectService projectService;

    public TimelineController(ProjectService projectService) {
        this.projectService = projectService;
    }

    @GetMapping
    public String timeline(Model model) {
        model.addAttribute("title", "<PERSON><PERSON>");
        model.addAttribute("projects", projectService.getAllProjects());
        return "timeline";
    }
} 