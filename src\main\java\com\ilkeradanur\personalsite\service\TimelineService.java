package com.ilkeradanur.personalsite.service;

import com.ilkeradanur.personalsite.entity.Timeline;
import com.ilkeradanur.personalsite.repository.TimelineRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class TimelineService {

    private final TimelineRepository timelineRepository;

    @Autowired
    public TimelineService(TimelineRepository timelineRepository) {
        this.timelineRepository = timelineRepository;
    }

    public List<Timeline> getAllTimelineItems() {
        return timelineRepository.findByIsActiveOrderByStartDateDesc(true);
    }

    public List<Timeline> getTimelineItemsByType(Timeline.TimelineType type) {
        return timelineRepository.findByTypeAndIsActiveOrderByStartDateDesc(type, true);
    }

    public Optional<Timeline> getTimelineItemById(Long id) {
        return timelineRepository.findById(id);
    }

    public Timeline saveTimelineItem(Timeline timeline) {
        if (timeline.getDisplayOrder() == null) {
            List<Timeline> items = timelineRepository.findAll();
            timeline.setDisplayOrder(items.size() + 1);
        }
        return timelineRepository.save(timeline);
    }

    public void deleteTimelineItem(Long id) {
        timelineRepository.deleteById(id);
    }

    @Transactional
    public void updateTimelineOrder(List<Long> timelineIds) {
        for (int i = 0; i < timelineIds.size(); i++) {
            Timeline timeline = timelineRepository.findById(timelineIds.get(i)).orElse(null);
            if (timeline != null) {
                timeline.setDisplayOrder(i + 1);
                timelineRepository.save(timeline);
            }
        }
    }
} 