package com.ilkeradanur.personal_website.repository;

import com.ilkeradanur.personal_website.entity.Skill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SkillRepository extends JpaRepository<Skill, Long> {
    
    // Kategoriye göre yetenekleri getir
    List<Skill> findByCategory(String category);
    
    // Aktif yetenekleri getir
    List<Skill> findByIsActiveTrue();
    
    // Kategoriye göre aktif yetenekleri getir
    List<Skill> findByCategoryAndIsActiveTrue(String category);
    
    // Kategoriye göre yetenekleri say
    @Query("SELECT s.category, COUNT(s) FROM Skill s GROUP BY s.category")
    List<Object[]> countSkillsByCategory();
    
    // Sertifikalı yetenekleri getir
    @Query("SELECT s FROM Skill s WHERE s.certificationName IS NOT NULL AND s.isActive = true")
    List<Skill> findCertifiedSkills();
} 