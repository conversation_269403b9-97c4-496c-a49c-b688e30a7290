package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Message;
import java.util.List;
import com.ilkeradanur.personal_website.dto.ContactFormDTO;

public interface MessageService {
    // Temel CRUD işlemleri
    Message saveMessage(Message message);
    List<Message> getAllMessages();
    void deleteMessage(Long id);
    
    // Mesaj durumu işlemleri
    List<Message> getUnreadMessages();
    Message markAsRead(Long id);
    Message saveMessageFromDTO(ContactFormDTO contactFormDTO);
    // Diğer servis metotları buraya eklenebilir
} 