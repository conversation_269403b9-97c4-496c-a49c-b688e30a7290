package com.ilkeradanur.personalsite.repository;

import com.ilkeradanur.personalsite.entity.Skill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface SkillRepository extends JpaRepository<Skill, Long> {
    List<Skill> findByIsActiveOrderByDisplayOrderAsc(Boolean isActive);
    List<Skill> findByCategoryAndIsActiveOrderByDisplayOrderAsc(String category, Boolean isActive);
} 