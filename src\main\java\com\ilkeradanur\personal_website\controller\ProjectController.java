package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.ProjectStatus;
import com.ilkeradanur.personal_website.service.ProjectService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/projects")
public class ProjectController {
    private final ProjectService projectService;

    public ProjectController(ProjectService projectService) {
        this.projectService = projectService;
    }

    // View endpoints
    @GetMapping("/timeline")
    public String timeline(Model model) {
        List<Project> allProjectsForTimeline = projectService.getAllProjects().stream().filter(p -> p != null).collect(Collectors.toList());
        model.addAttribute("projects", allProjectsForTimeline);
        model.addAttribute("title", "<PERSON><PERSON> Çizelgesi");
        return "timeline";
    }

    @GetMapping("/active")
    public String activeProjects(Model model) {
        model.addAttribute("projects", projectService.getActiveProjects());
        model.addAttribute("title", "Aktif Projeler");
        return "projects";
    }

    @GetMapping("/latest")
    public String latestProjects(Model model) {
        model.addAttribute("projects", projectService.getLatestProjects());
        model.addAttribute("title", "Son Eklenen Projeler");
        return "projects";
    }

    @GetMapping("/category/{category}")
    public String projectsByCategory(@PathVariable String category, Model model) {
        model.addAttribute("projects", projectService.getProjectsByCategory(category));
        model.addAttribute("title", category + " Kategorisindeki Projeler");
        model.addAttribute("category", category);
        return "projects";
    }

    @GetMapping("/technology/{technology}")
    public String projectsByTechnology(@PathVariable String technology, Model model) {
        model.addAttribute("projects", projectService.getProjectsByTechnology(technology));
        model.addAttribute("title", technology + " Teknolojisini Kullanan Projeler");
        model.addAttribute("technology", technology);
        return "projects";
    }

    @GetMapping("/search")
    public String searchProjects(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String technology,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Model model) {
        
        List<Project> projects;
        if (category != null && technology != null) {
            projects = projectService.getProjectsByCategoryAndTechnology(category, technology);
            model.addAttribute("title", category + " kategorisinde " + technology + " teknolojisini kullanan projeler");
        } else if (category != null) {
            projects = projectService.getProjectsByCategory(category);
            model.addAttribute("title", category + " kategorisindeki projeler");
        } else if (technology != null) {
            projects = projectService.getProjectsByTechnology(technology);
            model.addAttribute("title", technology + " teknolojisini kullanan projeler");
        } else if (startDate != null && endDate != null) {
            projects = projectService.getProjectsBetweenDates(startDate, endDate);
            model.addAttribute("title", startDate + " - " + endDate + " tarihleri arasındaki projeler");
        } else {
            projects = projectService.getAllProjects();
            model.addAttribute("title", "Tüm Projeler");
        }
        
        model.addAttribute("projects", projects);
        model.addAttribute("categoryStats", projectService.getProjectCountByCategory());
        model.addAttribute("technologyStats", projectService.getProjectCountByTechnology());
        return "projects";
    }

    @GetMapping
    public String allProjects(Model model) {
        List<Project> allProjects = projectService.getAllProjects().stream().filter(p -> p != null).collect(Collectors.toList());
        model.addAttribute("projects", allProjects);
        model.addAttribute("title", "Tüm Projeler");
        return "projects";
    }

    // REST API endpoints
    @GetMapping("/api")
    @ResponseBody
    public List<Project> getAllProjectsJson() {
        return projectService.getAllProjects();
    }

    @GetMapping("/api/active")
    @ResponseBody
    public List<Project> getActiveProjectsJson() {
        return projectService.getActiveProjects();
    }

    @GetMapping("/api/latest")
    @ResponseBody
    public List<Project> getLatestProjectsJson() {
        return projectService.getLatestProjects();
    }

    @GetMapping("/api/status/{status}")
    @ResponseBody
    public List<Project> getProjectsByStatusJson(@PathVariable ProjectStatus status) {
        return projectService.getProjectsByStatus(status);
    }

    @GetMapping("/api/category/{category}")
    @ResponseBody
    public List<Project> getProjectsByCategoryJson(@PathVariable String category) {
        return projectService.getProjectsByCategory(category);
    }

    @GetMapping("/api/technology/{technology}")
    @ResponseBody
    public List<Project> getProjectsByTechnologyJson(@PathVariable String technology) {
        return projectService.getProjectsByTechnology(technology);
    }

    @GetMapping("/api/technologies")
    @ResponseBody
    public List<Project> getProjectsByTechnologiesJson(@RequestParam Set<String> technologies) {
        return projectService.getProjectsByTechnologies(technologies);
    }

    @GetMapping("/api/category/{category}/technology/{technology}")
    @ResponseBody
    public List<Project> getProjectsByCategoryAndTechnologyJson(
            @PathVariable String category,
            @PathVariable String technology) {
        return projectService.getProjectsByCategoryAndTechnology(category, technology);
    }

    @GetMapping("/api/date-range")
    @ResponseBody
    public List<Project> getProjectsBetweenDatesJson(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return projectService.getProjectsBetweenDates(startDate, endDate);
    }

    @GetMapping("/api/stats/category")
    @ResponseBody
    public Map<String, Long> getProjectCountByCategoryJson() {
        return projectService.getProjectCountByCategory();
    }

    @GetMapping("/api/stats/technology")
    @ResponseBody
    public Map<String, Long> getProjectCountByTechnologyJson() {
        return projectService.getProjectCountByTechnology();
    }

    @PostMapping("/api")
    @ResponseBody
    public ResponseEntity<Project> createProject(@RequestBody Project project) {
        Project savedProject = projectService.saveProject(project);
        return ResponseEntity.ok(savedProject);
    }
} 