package com.ilkeradanur.personalsite.controller.admin;

import com.ilkeradanur.personalsite.entity.Skill;
import com.ilkeradanur.personalsite.service.SkillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
@RequestMapping("/admin/skills")
public class SkillController {

    private final SkillService skillService;

    @Autowired
    public SkillController(SkillService skillService) {
        this.skillService = skillService;
    }

    @GetMapping
    public String listSkills(Model model) {
        model.addAttribute("skills", skillService.getAllSkills());
        model.addAttribute("skill", new Skill());
        return "admin/skills";
    }

    @PostMapping
    public String saveSkill(@ModelAttribute Skill skill, RedirectAttributes redirectAttributes) {
        try {
            skillService.saveSkill(skill);
            redirectAttributes.addFlashAttribute("success", "Yetenek başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Yetenek kaydedilirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/skills";
    }

    @PostMapping("/{id}/delete")
    public String deleteSkill(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            skillService.deleteSkill(id);
            redirectAttributes.addFlashAttribute("success", "Yetenek başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Yetenek silinirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/skills";
    }
} 